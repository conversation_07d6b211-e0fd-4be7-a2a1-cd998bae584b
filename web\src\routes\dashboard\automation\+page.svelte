<script lang="ts">
  import { toast } from 'svelte-sonner';
  import { But<PERSON> } from '$lib/components/ui/button';
  import { Progress } from '$lib/components/ui/progress/index.js';
  import { Slider } from '$lib/components/ui/slider';
  import { Label } from '$lib/components/ui/label';
  import * as Tabs from '$lib/components/ui/tabs';
  import * as Select from '$lib/components/ui/select';
  import MultiCombobox from '$lib/components/ui/combobox/multi-combobox.svelte';
  import SEO from '$components/shared/SEO.svelte';
  import { Plus, Play, CheckCircle, AlertTriangle, X } from 'lucide-svelte';

  import AutomationRunSheet from './AutomationRunSheet.svelte';
  import AutomationRunsTab from './AutomationRunsTab.svelte';
  import ProfilesTab from './ProfilesTab.svelte';
  import FeatureGuard from '$components/features/EnhancedFeatureGuard.svelte';
  import { checkAutomationEligibility } from '$lib/utils/profileHelpers';
  import { writable } from 'svelte/store';
  import * as Dialog from '$lib/components/ui/dialog';
  import { superForm } from 'sveltekit-superforms';
  import { zodClient } from 'sveltekit-superforms/adapters';
  import { automationFormSchema } from '$lib/validators/automation.js';
  import { Switch } from '$lib/components/ui/switch';

  const { data } = $props();

  let profiles = $state(data.profiles || []);
  // Convert automationRuns to a proper Svelte store
  const automationRuns = writable(data.automationRuns || []);

  // Sheet state
  let selectedRun = $state(null);
  let isSheetOpen = $state(false);

  // Dialog state
  let createDialogOpen = $state(false);

  // Format server data for dropdowns
  const occupationOptions = $derived(() => {
    return data.occupations.map((occupation: any) => ({
      value: occupation.id,
      label: occupation.title,
    }));
  });

  const locationOptions = $derived(() => {
    return data.locations.map((city: any) => ({
      value: `${city.id}|${city.name}|${city.state.code}|${city.country}`,
      label: `${city.name}, ${city.state.code}`,
    }));
  });

  // Initialize superform
  const { form, enhance, submitting } = superForm(data.form, {
    validators: zodClient(automationFormSchema),
    dataType: 'json',
    resetForm: true,
    onSubmit: () => {
      toast.loading('Creating automation run...');
    },
    onResult: ({ result }) => {
      toast.dismiss();
      if (result.type === 'redirect') {
        toast.success('Automation run created successfully');
        createDialogOpen = false;
      } else if (result.type === 'failure') {
        toast.error(result.data?.error || 'Failed to create automation run');
      }
    },
    onError: () => {
      toast.dismiss();
      toast.error('An error occurred while creating the automation run');
    },
  });

  // Computed properties
  const selectedProfile = $derived(() => {
    return profiles.find((p) => p.id === $form.profileId);
  });

  const isProfileEligible = $derived(() => {
    if (!selectedProfile()) return false;
    const eligibility = checkAutomationEligibility(selectedProfile());
    return eligibility.isEligible;
  });

  // Search functions for dynamic loading (optional - for future use)
  async function searchOccupations(search = ''): Promise<any[]> {
    try {
      const response = await fetch(
        `/api/occupations?search=${encodeURIComponent(search)}&limit=20`
      );
      if (response.ok) {
        const searchData = await response.json();
        return searchData.map((occupation: any) => ({
          value: occupation.id,
          label: occupation.title,
        }));
      }
    } catch (error) {
      console.error('Error searching occupations:', error);
    }
    return [];
  }

  async function searchLocations(search = '') {
    try {
      const response = await fetch(`/api/locations?search=${encodeURIComponent(search)}&limit=20`);
      if (response.ok) {
        const searchData = await response.json();
        return searchData.map((city: any) => ({
          value: `${city.id}|${city.name}|${city.state.code}|${city.country}`,
          label: `${city.name}, ${city.state.code}`,
        }));
      }
    } catch (error) {
      console.error('Error searching locations:', error);
    }
    return [];
  }

  // Form validation
  const isFormValid = $derived(() => {
    // Must have a profile selected and it must be eligible
    if (!$form.profileId || !isProfileEligible()) return false;

    // Must have either keywords or location (or both)
    if ($form.keywords.length === 0 && $form.locations.length === 0) return false;

    // Salary validation - ensure min is not greater than max
    if ($form.salaryRange[0] > $form.salaryRange[1]) return false;

    // Experience validation - ensure min is not greater than max
    if ($form.experienceRange[0] > $form.experienceRange[1]) return false;

    return true;
  });

  // Get suggestions from selected profile
  const profileSuggestions = $derived(() => {
    if (!selectedProfile()?.data?.data) return null;

    const profileData = selectedProfile().data.data as any;

    // Extract job title suggestions from work experience
    const jobTitles =
      profileData.workExperience?.map((exp: any) => exp.title).filter(Boolean) || [];

    // Extract skills for keyword suggestions
    const skills = profileData.skillsData?.list || profileData.skills || [];

    // Calculate total experience years
    let totalExperience = 0;
    if (profileData.workExperience) {
      profileData.workExperience.forEach((exp: any) => {
        if (exp.startDate && exp.endDate) {
          const start = new Date(exp.startDate);
          const end = exp.current ? new Date() : new Date(exp.endDate);
          const years = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 365);
          totalExperience += years;
        }
      });
    }

    // Get location from profile
    const location = profileData.personalInfo?.city || profileData.personalInfo?.address || '';

    return {
      jobTitles: [...new Set(jobTitles)].slice(0, 3), // Top 3 unique job titles
      skills: Array.isArray(skills) ? skills.slice(0, 5) : [], // Top 5 skills
      experienceYears: Math.floor(totalExperience),
      location,
    };
  });

  // Auto-fill suggestions when profile is selected
  function applySuggestions() {
    const suggestions = profileSuggestions();
    if (!suggestions) return;

    // Get current occupation options
    const currentOccupationOptions = occupationOptions();

    // Suggest keywords based on latest job title and top skills
    if (suggestions.jobTitles.length > 0 && suggestions.skills.length > 0) {
      const suggestedTitles = [suggestions.jobTitles[0], ...suggestions.skills.slice(0, 2)];

      // Find matching occupation IDs for the suggested titles
      const matchingOccupations: string[] = [];
      for (const title of suggestedTitles) {
        const match = currentOccupationOptions.find(
          (opt: { value: string; label: string }) =>
            opt.label.toLowerCase().includes(title.toLowerCase()) ||
            title.toLowerCase().includes(opt.label.toLowerCase())
        );
        if (match) {
          matchingOccupations.push(match.value);
        }
      }

      if (matchingOccupations.length > 0) {
        $form.keywords = matchingOccupations;
      }
    }

    // Suggest location - we'll need to format it properly for the location select
    if (suggestions.location) {
      // For now, we'll add it as a simple location
      // In a real implementation, you might want to search for matching cities
      $form.locations = [`custom|${suggestions.location}|${suggestions.location}|US`];
    }

    // Suggest experience range based on calculated experience
    if (suggestions.experienceYears > 0) {
      const minExp = Math.max(0, suggestions.experienceYears - 2);
      const maxExp = Math.min(15, suggestions.experienceYears + 3);
      $form.experienceRange = [minExp, maxExp];
    }
  }

  // Function to handle refreshing a run
  function handleRunRefresh(updatedRun: any) {
    automationRuns.update((runs) =>
      runs.map((run: any) => (run.id === updatedRun.id ? updatedRun : run))
    );
  }

  // Data is now loaded from the server, no need for client-side initialization
</script>

<SEO
  title="Job Automation | Hirli"
  description="Automate your job search and application process with Hirli's intelligent automation tools."
  keywords="job automation, automated job search, job application automation, resume matching, career automation, job search tools" />

<div class="flex w-full flex-col">
  <div class="flex flex-col gap-8 p-4">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold">Automation</h1>
        <p class="text-muted-foreground text-sm">
          Automate your job search and application process
        </p>
      </div>
      <FeatureGuard
        userData={data.user}
        featureId="automation"
        limitId="automation_runs_per_month"
        showUpgradePrompt={true}
        fallbackMessage="Automation features are not available in your current plan">
        <Button variant="default" onclick={() => (createDialogOpen = true)}>
          <Play class="mr-2 h-4 w-4" />
          New Automation Run
        </Button>
      </FeatureGuard>
    </div>
  </div>

  <Tabs.Root value="runs">
    <Tabs.List>
      <Tabs.Trigger value="runs">Automation Runs</Tabs.Trigger>
      <Tabs.Trigger value="profiles">Available Profiles</Tabs.Trigger>
    </Tabs.List>

    <Tabs.Content value="runs" class="mt-0 p-4">
      <AutomationRunsTab
        userData={data.user}
        {automationRuns}
        onRunSelect={(run) => {
          selectedRun = run;
          isSheetOpen = true;
        }}
        onCreateRun={() => (createDialogOpen = true)} />
    </Tabs.Content>

    <Tabs.Content value="profiles" class="mt-0 p-4">
      <ProfilesTab
        userData={data.user}
        {profiles}
        onProfileSelect={(profileId) => {
          $form.profileId = profileId;
          createDialogOpen = true;
        }} />
    </Tabs.Content>
  </Tabs.Root>
</div>

<!-- Create Automation Run Dialog -->
<Dialog.Root bind:open={createDialogOpen}>
  <Dialog.Overlay />
  <Dialog.Content class="max-h-[90vh] max-w-4xl overflow-y-auto">
    <FeatureGuard
      userData={data.user}
      featureId="automation"
      limitId="automation_runs_per_month"
      showUpgradePrompt={true}
      fallbackMessage="Automation features are not available in your current plan">
      <Dialog.Header>
        <Dialog.Title>Configure Automation Run</Dialog.Title>
        <Dialog.Description>
          Set up detailed automation specifications for intelligent job matching and application.
        </Dialog.Description>
      </Dialog.Header>

      <form method="POST" use:enhance class="space-y-4">
        <!-- Hidden form fields -->
        <input type="hidden" name="profileId" bind:value={$form.profileId} />
        <input type="hidden" name="keywords" value={JSON.stringify($form.keywords)} />
        <input type="hidden" name="locations" value={JSON.stringify($form.locations)} />
        <input type="hidden" name="maxJobsToApply" bind:value={$form.maxJobsToApply} />
        <input type="hidden" name="minMatchScore" bind:value={$form.minMatchScore} />
        <input type="hidden" name="autoApplyEnabled" value={$form.autoApplyEnabled} />
        <input type="hidden" name="salaryRange" value={JSON.stringify($form.salaryRange)} />
        <input type="hidden" name="experienceRange" value={JSON.stringify($form.experienceRange)} />
        <input type="hidden" name="jobTypes" value={JSON.stringify($form.jobTypes)} />
        <input type="hidden" name="remotePreference" bind:value={$form.remotePreference} />
        <input
          type="hidden"
          name="companySizePreference"
          value={JSON.stringify($form.companySizePreference)} />
        <input
          type="hidden"
          name="excludeCompanies"
          value={JSON.stringify($form.excludeCompanies)} />
        <input
          type="hidden"
          name="preferredCompanies"
          value={JSON.stringify($form.preferredCompanies)} />

        <div class="grid gap-4">
          <!-- Profile Selection -->
          <div class="grid gap-1">
            <div class="flex items-center justify-between">
              <label for="profile" class="text-sm font-medium">Profile *</label>
              <a
                href="/dashboard/settings/profile"
                class="text-xs text-blue-500 underline hover:text-blue-400">
                Manage Profiles
              </a>
            </div>
            <Select.Root
              type="single"
              value={$form.profileId}
              onValueChange={(value) => {
                $form.profileId = value || '';
              }}>
              <Select.Trigger class="w-full p-2">
                <Select.Value
                  placeholder={profiles.find((p) => p.id === $form.profileId)?.name ||
                    'Select a profile'} />
              </Select.Trigger>
              <Select.Content class="max-h-60">
                {#each profiles as profile (profile.id)}
                  <Select.Item value={profile.id}>
                    {profile.name}
                  </Select.Item>
                {/each}
              </Select.Content>
            </Select.Root>
          </div>

          <!-- Profile Eligibility Check -->
          {#if $form.profileId}
            {@const selectedProfile = profiles.find((p) => p.id === $form.profileId)}
            {#if selectedProfile}
              {@const eligibility = checkAutomationEligibility(selectedProfile)}
              <div class="rounded-lg border p-4">
                <div class="mb-2 flex items-center gap-2">
                  {#if eligibility.isEligible}
                    <CheckCircle class="h-5 w-5 text-green-500" />
                    <span class="font-medium text-green-700">Profile Eligible for Automation</span>
                  {:else}
                    <AlertTriangle class="h-5 w-5 text-orange-500" />
                    <span class="font-medium text-orange-700">Profile Needs Completion</span>
                  {/if}
                </div>

                <div class="mb-3">
                  <div class="mb-1 flex items-center justify-between text-sm">
                    <span>Profile Completion</span>
                    <span>{eligibility.completionPercentage}%</span>
                  </div>
                  <Progress value={eligibility.completionPercentage} max={100} />
                </div>

                {#if !eligibility.isEligible}
                  <div class="space-y-1">
                    <p class="text-sm font-medium text-gray-700">Missing Requirements:</p>
                    {#each eligibility.missingRequirements as requirement}
                      <div class="flex items-center gap-2 text-sm text-gray-600">
                        <X class="h-3 w-3 text-red-500" />
                        {requirement}
                      </div>
                    {/each}
                  </div>
                {/if}
              </div>
            {/if}
          {/if}

          <!-- Automation Settings -->
          {#if $form.profileId && isProfileEligible()}
            <!-- Search Criteria Section -->
            <div class="space-y-6">
              <div class="bg-card rounded-lg border p-6">
                <div class="mb-4 flex items-center justify-between">
                  <h3 class="text-lg font-semibold">Search Criteria</h3>
                  {#if profileSuggestions() && profileSuggestions().jobTitles.length > 0}
                    <Button variant="outline" size="sm" onclick={applySuggestions} class="text-xs">
                      <Plus class="mr-1 h-3 w-3" />
                      Use Profile Suggestions
                    </Button>
                  {/if}
                </div>

                <div class="grid gap-6 md:grid-cols-2">
                  <div class="space-y-2">
                    <label for="keywords" class="text-sm font-medium">Job Keywords *</label>
                    <MultiCombobox
                      placeholder="Search for occupations..."
                      selectedValues={$form.keywords}
                      options={occupationOptions()}
                      onSelectedValuesChange={(values) => ($form.keywords = values)}
                      searchOptions={searchOccupations}
                      maxDisplayItems={1}
                      width="w-full" />
                    {#if profileSuggestions() && profileSuggestions().jobTitles.length > 0}
                      <p class="text-muted-foreground text-xs">
                        Suggestions: {profileSuggestions().jobTitles.join(', ')}
                      </p>
                    {/if}
                  </div>

                  <div class="space-y-2">
                    <label for="location" class="text-sm font-medium">Locations</label>
                    <MultiCombobox
                      placeholder="Search for cities..."
                      selectedValues={$form.locations}
                      options={locationOptions()}
                      onSelectedValuesChange={(values) => ($form.locations = values)}
                      searchOptions={searchLocations}
                      maxDisplayItems={1}
                      width="w-full" />
                    {#if profileSuggestions() && profileSuggestions().location}
                      <p class="text-muted-foreground text-xs">
                        From profile: {profileSuggestions().location}
                      </p>
                    {/if}
                  </div>
                </div>
              </div>

              <!-- Automation Settings Section -->
              <div class="bg-card rounded-lg border p-6">
                <h3 class="mb-4 text-lg font-semibold">Automation Settings</h3>

                <div class="grid gap-6 md:grid-cols-2">
                  <!-- Left Column -->
                  <div class="space-y-6">
                    <div class="space-y-3">
                      <Label class="text-sm font-medium">
                        Maximum Jobs to Apply: <span class="text-primary font-semibold"
                          >{$form.maxJobsToApply}</span>
                      </Label>
                      <Slider
                        type="single"
                        bind:value={$form.maxJobsToApply}
                        min={1}
                        max={50}
                        step={1}
                        class="w-full" />
                      <div class="text-muted-foreground flex justify-between text-xs">
                        <span>1 job</span>
                        <span>50 jobs</span>
                      </div>
                    </div>

                    <div class="space-y-3">
                      <Label class="text-sm font-medium">
                        Minimum Match Score: <span class="text-primary font-semibold"
                          >{$form.minMatchScore}%</span>
                      </Label>
                      <Slider
                        type="single"
                        bind:value={$form.minMatchScore}
                        min={60}
                        max={95}
                        step={5}
                        class="w-full" />
                      <div class="text-muted-foreground flex justify-between text-xs">
                        <span>60%</span>
                        <span>95%</span>
                      </div>
                    </div>
                  </div>

                  <!-- Right Column -->
                  <div class="space-y-6">
                    <div class="space-y-3">
                      <Label class="text-sm font-medium">
                        Salary Range: <span class="text-primary font-semibold"
                          >${$form.salaryRange[0]}k - ${$form.salaryRange[1]}k</span>
                      </Label>
                      <Slider
                        type="multiple"
                        bind:value={$form.salaryRange}
                        min={30}
                        max={250}
                        step={5}
                        class="w-full" />
                      <div class="text-muted-foreground flex justify-between text-xs">
                        <span>$30k</span>
                        <span>$250k+</span>
                      </div>
                    </div>

                    <div class="space-y-3">
                      <Label class="text-sm font-medium">
                        Experience Range: <span class="text-primary font-semibold"
                          >{$form.experienceRange[0]} - {$form.experienceRange[1]} years</span>
                      </Label>
                      <Slider
                        type="multiple"
                        bind:value={$form.experienceRange}
                        min={0}
                        max={15}
                        step={1}
                        class="w-full" />
                      <div class="text-muted-foreground flex justify-between text-xs">
                        <span>0 years</span>
                        <span>15+ years</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Auto-Apply Toggle -->
                <div
                  class="bg-muted/50 mt-6 flex items-center justify-between rounded-lg border p-4">
                  <div>
                    <Label for="auto-apply" class="text-sm font-medium"
                      >Enable Automatic Applications</Label>
                    <p class="text-muted-foreground text-xs">
                      Automatically apply to jobs that match your criteria
                    </p>
                  </div>
                  <Switch
                    id="auto-apply"
                    checked={Boolean($form.autoApplyEnabled)}
                    onCheckedChange={(checked) => {
                      $form.autoApplyEnabled = checked;
                    }} />
                </div>
              </div>
            </div>
          {/if}
        </div>

        <Dialog.Footer class="grid grid-cols-2 gap-4">
          <Button variant="outline" onclick={() => (createDialogOpen = false)}>Cancel</Button>
          <Button type="submit" variant="default" disabled={!isFormValid() || $submitting}>
            {#if $submitting}
              Creating...
            {:else}
              Start Automation
            {/if}
          </Button>
        </Dialog.Footer>
      </form>
    </FeatureGuard>
  </Dialog.Content>
</Dialog.Root>

<!-- Automation Run Sheet -->
{#if selectedRun}
  <AutomationRunSheet
    bind:open={isSheetOpen}
    automationRun={selectedRun}
    onClose={() => {
      selectedRun = null;
    }}
    onRefresh={handleRunRefresh}
    onStop={() => {
      automationRuns.update((runs) =>
        runs.map((run: any) => {
          if (run.id === selectedRun.id) {
            return { ...run, status: 'stopped' };
          }
          return run;
        })
      );
    }} />
{/if}
