<script lang="ts">
  import { toast } from 'svelte-sonner';
  import { writable } from 'svelte/store';
  import { onMount } from 'svelte';
  import Button from '$lib/components/ui/button/button.svelte';
  import * as Card from '$lib/components/ui/card';
  import { Badge } from '$lib/components/ui/badge';

  import { formatDate, formatDistanceToNow } from '$lib/utils';
  import {
    Play,
    StopCircle,
    RefreshCw,
    ArrowLeft,
    FileText,
    Briefcase,
    CheckCircle,
    XCircle,
    Clock,
    ExternalLink,
    Building,
    MapPin,
    DollarSign,
    Calendar,
    Target,
  } from 'lucide-svelte';

  const { data } = $props<{ data: any }>();
  let automationRun = writable(data.automationRun);
  let isUpdatingSettings = $state(false);
  let selectedJobsForAutoApply = $state(new Set<string>());
  let showAutoApplyConfirm = $state(false);

  // Mock jobs data for UX testing
  const mockJobs = [
    {
      id: '1',
      title: 'Senior Frontend Developer',
      company: 'TechCorp Inc.',
      location: 'San Francisco, CA',
      salary: '$120k - $160k',
      salaryMin: 120,
      salaryMax: 160,
      employmentType: 'full-time',
      postedDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      matchScore: 92,
      description:
        'We are looking for a Senior Frontend Developer to join our team. You will be responsible for building user interfaces using React, TypeScript, and modern web technologies.',
      skills: ['React', 'TypeScript', 'JavaScript', 'CSS', 'HTML', 'Node.js'],
      applyLink: 'https://example.com/apply/1',
      applicationStatus: null,
    },
    {
      id: '2',
      title: 'Full Stack Engineer',
      company: 'StartupXYZ',
      location: 'Remote',
      salary: '$100k - $140k',
      salaryMin: 100,
      salaryMax: 140,
      employmentType: 'full-time',
      postedDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
      matchScore: 88,
      description:
        'Join our fast-growing startup as a Full Stack Engineer. Work with React, Node.js, and PostgreSQL to build scalable web applications.',
      skills: ['React', 'Node.js', 'PostgreSQL', 'JavaScript', 'AWS'],
      applyLink: 'https://example.com/apply/2',
      applicationStatus: null,
    },
    {
      id: '3',
      title: 'React Developer',
      company: 'Digital Agency Co.',
      location: 'New York, NY',
      salary: '$90k - $120k',
      salaryMin: 90,
      salaryMax: 120,
      employmentType: 'full-time',
      postedDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      matchScore: 85,
      description:
        'We need a React Developer to help build modern web applications for our clients. Experience with Redux and TypeScript preferred.',
      skills: ['React', 'Redux', 'TypeScript', 'CSS', 'JavaScript'],
      applyLink: 'https://example.com/apply/3',
      applicationStatus: null,
    },
    {
      id: '4',
      title: 'Frontend Engineer',
      company: 'Enterprise Solutions Ltd.',
      location: 'Austin, TX',
      salary: '$110k - $150k',
      salaryMin: 110,
      salaryMax: 150,
      employmentType: 'full-time',
      postedDate: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
      matchScore: 78,
      description:
        'Looking for a Frontend Engineer to work on enterprise-level applications. Strong knowledge of React and modern JavaScript required.',
      skills: ['React', 'JavaScript', 'HTML', 'CSS', 'Git'],
      applyLink: 'https://example.com/apply/4',
      applicationStatus: null,
    },
    {
      id: '5',
      title: 'Software Developer',
      company: 'MegaCorp Industries',
      location: 'Seattle, WA',
      salary: '$95k - $130k',
      salaryMin: 95,
      salaryMax: 130,
      employmentType: 'full-time',
      postedDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      matchScore: 72,
      description:
        'Join our development team to build innovative software solutions. Experience with React and backend technologies is a plus.',
      skills: ['React', 'JavaScript', 'Python', 'SQL'],
      applyLink: 'https://example.com/apply/5',
      applicationStatus: null,
    },
  ];

  // Use mock jobs for UI testing, fallback to real data
  const jobsToDisplay = mockJobs.length > 0 ? mockJobs : data.jobs || [];

  // Function to update automation settings
  async function updateAutomationSettings(settings: any) {
    if (!$automationRun || !$automationRun.id) return;

    isUpdatingSettings = true;

    try {
      const response = await fetch(`/api/automation/runs/${$automationRun.id}/settings`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        const updatedRun = await response.json();
        automationRun.set(updatedRun);
        toast.success('Settings updated successfully');
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to update settings');
      }
    } catch (error) {
      console.error('Error updating settings:', error);
      toast.error('An error occurred while updating settings');
    } finally {
      isUpdatingSettings = false;
    }
  }

  // Function to toggle job selection for auto-apply
  function toggleJobSelection(jobId: string) {
    if (selectedJobsForAutoApply.has(jobId)) {
      selectedJobsForAutoApply.delete(jobId);
    } else {
      selectedJobsForAutoApply.add(jobId);
    }
    // Trigger reactivity
    selectedJobsForAutoApply = new Set(selectedJobsForAutoApply);
  }

  // Function to select all jobs above a certain match score
  function selectJobsByMatchScore(minScore: number = 80) {
    selectedJobsForAutoApply.clear();
    jobsToDisplay.forEach((job: any) => {
      if (job.matchScore && job.matchScore >= minScore) {
        selectedJobsForAutoApply.add(job.id);
      }
    });
    selectedJobsForAutoApply = new Set(selectedJobsForAutoApply);
  }

  // Function to clear all selections
  function clearAllSelections() {
    selectedJobsForAutoApply.clear();
    selectedJobsForAutoApply = new Set(selectedJobsForAutoApply);
  }

  // Function to show auto-apply confirmation
  function showAutoApplyConfirmation() {
    if (selectedJobsForAutoApply.size === 0) {
      toast.error('Please select at least one job to enable auto-apply');
      return;
    }
    showAutoApplyConfirm = true;
  }

  // Function to confirm and enable auto-apply
  async function confirmAutoApply() {
    try {
      // Here you would make an API call to enable auto-apply for selected jobs
      const selectedJobs = Array.from(selectedJobsForAutoApply);

      // Mock API call - replace with actual implementation
      console.log('Enabling auto-apply for jobs:', selectedJobs);

      toast.success(
        `Auto-apply enabled for ${selectedJobs.length} job${selectedJobs.length === 1 ? '' : 's'}`
      );
      showAutoApplyConfirm = false;

      // Update the automation run to reflect auto-apply is now enabled
      automationRun.update((run) => ({
        ...run,
        autoApplyEnabled: true,
        selectedJobIds: selectedJobs,
      }));
    } catch (error) {
      console.error('Error enabling auto-apply:', error);
      toast.error('Failed to enable auto-apply');
    }
  }

  // Function to stop an automation run
  async function stopAutomationRun() {
    try {
      const response = await fetch(`/api/automation/runs/${$automationRun.id}/stop`, {
        method: 'POST',
      });

      if (response.ok) {
        const updatedRun = await response.json();
        automationRun.set({
          ...$automationRun,
          status: 'stopped',
          stoppedAt: updatedRun.stoppedAt,
        });
        toast.success('Automation run stopped');
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to stop automation run');
      }
    } catch (error) {
      console.error('Error stopping automation run:', error);
      toast.error('An error occurred while stopping the automation run');
    }
  }

  // Function to refresh the automation run data
  async function refreshData() {
    try {
      const response = await fetch(`/api/automation/runs/${$automationRun.id}`);
      if (response.ok) {
        const updatedRun = await response.json();
        automationRun.set(updatedRun);
        toast.success('Data refreshed');

        // Set up auto-refresh if the run is still in progress
        if (updatedRun.status === 'running' || updatedRun.status === 'pending') {
          setTimeout(refreshData, 5000); // Refresh every 5 seconds
        }
      } else {
        toast.error('Failed to refresh data');
      }
    } catch (error) {
      console.error('Error refreshing data:', error);
      toast.error('An error occurred while refreshing data');
    }
  }

  // WebSocket connection for real-time updates
  let socket: WebSocket | null = null;

  // Connect to WebSocket when the component mounts
  onMount(() => {
    // Create WebSocket connection
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/api/automation/ws`;
    socket = new WebSocket(wsUrl);

    socket.onopen = () => {
      console.log('WebSocket connection established');
    };

    socket.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        console.log('WebSocket message received:', message);

        if (message.type === 'status' && message.data.runId === $automationRun.id) {
          // Update automation run status
          automationRun.update((run) => ({
            ...run,
            status: message.data.status,
            progress: message.data.progress || run.progress,
            error: message.data.error || run.error,
          }));
        } else if (message.type === 'jobs' && message.data.runId === $automationRun.id) {
          // Update job listings
          refreshData(); // Refresh to get the latest jobs
        }
      } catch (error) {
        console.error('Error processing WebSocket message:', error);
      }
    };

    socket.onclose = () => {
      console.log('WebSocket connection closed');
    };

    socket.onerror = (error) => {
      console.error('WebSocket error:', error);
      // Fall back to polling if WebSocket fails
      if (
        $automationRun &&
        ($automationRun.status === 'running' || $automationRun.status === 'pending')
      ) {
        setTimeout(refreshData, 5000); // Refresh every 5 seconds
      }
    };

    return () => {
      // Close WebSocket when component unmounts
      if (socket && socket.readyState === WebSocket.OPEN) {
        socket.close();
      }
    };
  });

  // Auto-refresh if the run is in progress and WebSocket is not available
  $effect(() => {
    if (
      $automationRun &&
      ($automationRun.status === 'running' || $automationRun.status === 'pending') &&
      (!socket || socket.readyState !== WebSocket.OPEN)
    ) {
      setTimeout(refreshData, 5000); // Refresh every 5 seconds
    }
  });

  // Function to get status badge variant
  function getStatusVariant(status: string) {
    switch (status) {
      case 'running':
        return 'default';
      case 'completed':
        return 'outline';
      case 'failed':
        return 'destructive';
      case 'stopped':
        return 'secondary';
      default:
        return 'secondary';
    }
  }

  // Function to get status icon
  function getStatusIcon(status: string) {
    switch (status) {
      case 'running':
        return Play;
      case 'completed':
        return CheckCircle;
      case 'failed':
        return XCircle;
      case 'stopped':
        return StopCircle;
      case 'pending':
        return Clock;
      default:
        return Clock;
    }
  }

  // Function to calculate progress percentage
  function calculateProgress(run: any) {
    if (run.status === 'completed') return 100;
    if (run.status === 'failed' || run.status === 'stopped') return run.progress || 0;
    return run.progress || 0;
  }

  // Type definition for profile data
  type ProfileData = {
    fullName?: string;
    title?: string;
    headline?: string;
    skills?: string[];
  };

  // Helper function to safely access profile data
  function getProfileData(profile: any): ProfileData {
    if (!profile) return {};

    // Check if profile has a data property (nested structure)
    if (profile.data) {
      try {
        // Try to parse the data if it's a string
        if (typeof profile.data.data === 'string') {
          return JSON.parse(profile.data.data);
        }
        // If data.data exists and is an object
        if (profile.data.data && typeof profile.data.data === 'object') {
          return profile.data.data as ProfileData;
        }
        // If data is directly the profile data
        if (typeof profile.data === 'string') {
          return JSON.parse(profile.data);
        }
        return profile.data as ProfileData;
      } catch (e) {
        console.error('Error parsing profile data:', e);
        return {};
      }
    }

    // Fallback: return profile directly if no nested data
    return profile as ProfileData;
  }
</script>

<div class="container mt-6 flex max-w-full flex-col gap-10 p-6">
  <div class="flex flex-col gap-8">
    <div class="flex items-center gap-4">
      <Button variant="ghost" onclick={() => (window.location.href = '/dashboard/automation')}>
        <ArrowLeft class="mr-2 h-4 w-4" />
        Back to Automation
      </Button>

      <h1 class="text-xl font-normal text-white">Automation Run Details</h1>

      <Badge variant={getStatusVariant($automationRun.status)} class="ml-2">
        {@const StatusIcon = getStatusIcon($automationRun.status)}
        <StatusIcon class="mr-1 h-3 w-3" />
        {$automationRun.status.charAt(0).toUpperCase() + $automationRun.status.slice(1)}
      </Badge>

      <div class="ml-auto flex items-center gap-2">
        {#if $automationRun.status === 'running'}
          <Button variant="outline" size="sm" onclick={stopAutomationRun}>
            <StopCircle class="mr-2 h-4 w-4" />
            Stop Run
          </Button>
        {:else if $automationRun.status === 'pending'}
          <Button variant="outline" size="sm" disabled>
            <Clock class="mr-2 h-4 w-4" />
            Pending
          </Button>
        {/if}
        <Button variant="outline" size="sm" onclick={refreshData}>
          <RefreshCw class="mr-2 h-4 w-4" />
          Refresh
        </Button>
      </div>
    </div>
  </div>

  <Card.Root>
    <Card.Header class="p-6">
      <Card.Title>Run Information</Card.Title>
      <Card.Description>Details about this automation run</Card.Description>
    </Card.Header>
    <Card.Content class="p-6 pt-0">
      <div class="mb-4">
        <div class="bg-secondary h-2 w-full rounded-full">
          <div
            class="bg-primary h-full rounded-full transition-all"
            style="width: {calculateProgress($automationRun)}%">
          </div>
        </div>
        <div class="mt-2 text-sm text-gray-400">
          Progress: {calculateProgress($automationRun)}%
        </div>
      </div>

      <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div>
          <h3 class="mb-2 text-lg font-semibold">Profile</h3>
          <div class="rounded-lg border p-4">
            <div class="mb-2 text-lg font-medium">
              {$automationRun.profile
                ? getProfileData($automationRun.profile).fullName || 'Unnamed Profile'
                : 'Unnamed Profile'}
            </div>
            <div class="mb-4 text-sm text-gray-400">
              {$automationRun.profile
                ? getProfileData($automationRun.profile).title ||
                  getProfileData($automationRun.profile).headline ||
                  'No title specified'
                : 'No title specified'}
            </div>

            <div class="mb-2 text-sm font-medium text-gray-400">Resume</div>
            <div class="mb-4">
              {#if $automationRun.profile?.resumes && Array.isArray($automationRun.profile.resumes) && $automationRun.profile.resumes.length > 0}
                <Badge variant="outline">
                  <FileText class="mr-1 h-3 w-3" />
                  {$automationRun.profile.resumes[0].document?.label || 'Resume'}
                </Badge>
              {:else}
                <Badge variant="outline" class="text-gray-400">No resume</Badge>
              {/if}
            </div>

            <div class="mb-2 text-sm font-medium text-gray-400">Skills</div>
            <div class="flex flex-wrap gap-1">
              {#if $automationRun.profile && getProfileData($automationRun.profile).skills && getProfileData($automationRun.profile).skills.length > 0}
                {#each getProfileData($automationRun.profile).skills.slice(0, 5) as skill}
                  <Badge variant="secondary" class="text-xs">{skill}</Badge>
                {/each}
                {#if getProfileData($automationRun.profile).skills.length > 5}
                  <Badge variant="secondary" class="text-xs"
                    >+{getProfileData($automationRun.profile).skills.length - 5} more</Badge>
                {/if}
              {:else}
                <span class="text-gray-400">No skills specified</span>
              {/if}
            </div>
          </div>
        </div>

        <div>
          <h3 class="mb-2 text-lg font-semibold">Search Parameters</h3>
          <div class="rounded-lg border p-4">
            <div class="mb-4">
              <div class="text-sm font-medium text-gray-400">Keywords</div>
              <div>{$automationRun.keywords || 'None specified'}</div>
            </div>

            <div class="mb-4">
              <div class="text-sm font-medium text-gray-400">Location</div>
              <div>{$automationRun.location || 'None specified'}</div>
            </div>

            <div class="mb-4">
              <div class="text-sm font-medium text-gray-400">Started</div>
              <div>
                {formatDate($automationRun.createdAt)} ({formatDistanceToNow(
                  new Date($automationRun.createdAt)
                )} ago)
              </div>
            </div>

            {#if $automationRun.stoppedAt}
              <div>
                <div class="text-sm font-medium text-gray-400">Stopped</div>
                <div>
                  {formatDate($automationRun.stoppedAt)} ({formatDistanceToNow(
                    new Date($automationRun.stoppedAt)
                  )} ago)
                </div>
              </div>
            {/if}
          </div>
        </div>
      </div>
    </Card.Content>
  </Card.Root>

  <!-- Automation Configuration -->
  <Card.Root>
    <Card.Header class="p-6">
      <Card.Title>Automation Configuration</Card.Title>
      <Card.Description>Detailed automation parameters and settings</Card.Description>
    </Card.Header>
    <Card.Content class="p-6 pt-0">
      <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        <!-- Auto-Apply Status -->
        <div class="rounded-lg border p-4">
          <h4 class="mb-3 flex items-center text-sm font-medium text-gray-400">
            <Target class="mr-2 h-4 w-4" />
            Auto-Apply Status
          </h4>
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <span class="text-sm">Auto-Apply Enabled</span>
              <Badge variant={$automationRun.autoApplyEnabled ? 'default' : 'secondary'}>
                {$automationRun.autoApplyEnabled ? 'Enabled' : 'Disabled'}
              </Badge>
            </div>
            {#if $automationRun.autoApplyEnabled}
              <div>
                <span class="text-xs text-gray-400">Jobs Selected</span>
                <div class="text-sm">{$automationRun.selectedJobIds?.length || 0}</div>
              </div>
            {/if}
            <div>
              <span class="text-xs text-gray-400">Max Jobs to Apply</span>
              <div class="text-sm">{$automationRun.maxJobsToApply || 10}</div>
            </div>
            <div>
              <span class="text-xs text-gray-400">Min Match Score</span>
              <div class="text-sm">{$automationRun.minMatchScore || 70}%</div>
            </div>
          </div>
        </div>

        <!-- Salary & Experience -->
        <div class="rounded-lg border p-4">
          <h4 class="mb-3 flex items-center text-sm font-medium text-gray-400">
            <DollarSign class="mr-2 h-4 w-4" />
            Salary & Experience
          </h4>
          <div class="space-y-3">
            {#if $automationRun.salaryMin || $automationRun.salaryMax}
              <div>
                <span class="text-xs text-gray-400">Salary Range</span>
                <div class="text-sm">
                  ${$automationRun.salaryMin || 0}k - ${$automationRun.salaryMax || 200}k
                </div>
              </div>
            {/if}
            {#if $automationRun.experienceLevelMin || $automationRun.experienceLevelMax}
              <div>
                <span class="text-xs text-gray-400">Experience Range</span>
                <div class="text-sm">
                  {$automationRun.experienceLevelMin || 0} - {$automationRun.experienceLevelMax ||
                    10} years
                </div>
              </div>
            {/if}
            {#if $automationRun.remotePreference}
              <div>
                <span class="text-xs text-gray-400">Remote Preference</span>
                <div class="text-sm capitalize">{$automationRun.remotePreference}</div>
              </div>
            {/if}
          </div>
        </div>

        <!-- Job Types & Company Preferences -->
        <div class="rounded-lg border p-4">
          <h4 class="mb-3 flex items-center text-sm font-medium text-gray-400">
            <Building class="mr-2 h-4 w-4" />
            Preferences
          </h4>
          <div class="space-y-3">
            {#if $automationRun.jobTypes && $automationRun.jobTypes.length > 0}
              <div>
                <span class="text-xs text-gray-400">Job Types</span>
                <div class="mt-1 flex flex-wrap gap-1">
                  {#each $automationRun.jobTypes as jobType}
                    <Badge variant="secondary" class="text-xs capitalize">{jobType}</Badge>
                  {/each}
                </div>
              </div>
            {/if}
            {#if $automationRun.companySizePreference && $automationRun.companySizePreference.length > 0}
              <div>
                <span class="text-xs text-gray-400">Company Size</span>
                <div class="mt-1 flex flex-wrap gap-1">
                  {#each $automationRun.companySizePreference as size}
                    <Badge variant="secondary" class="text-xs capitalize">{size}</Badge>
                  {/each}
                </div>
              </div>
            {/if}
            {#if $automationRun.preferredCompanies && $automationRun.preferredCompanies.length > 0}
              <div>
                <span class="text-xs text-gray-400">Preferred Companies</span>
                <div class="mt-1 flex flex-wrap gap-1">
                  {#each $automationRun.preferredCompanies.slice(0, 3) as company}
                    <Badge variant="outline" class="text-xs">{company}</Badge>
                  {/each}
                  {#if $automationRun.preferredCompanies.length > 3}
                    <Badge variant="outline" class="text-xs"
                      >+{$automationRun.preferredCompanies.length - 3} more</Badge>
                  {/if}
                </div>
              </div>
            {/if}
            {#if $automationRun.excludeCompanies && $automationRun.excludeCompanies.length > 0}
              <div>
                <span class="text-xs text-gray-400">Excluded Companies</span>
                <div class="mt-1 flex flex-wrap gap-1">
                  {#each $automationRun.excludeCompanies.slice(0, 3) as company}
                    <Badge variant="destructive" class="text-xs">{company}</Badge>
                  {/each}
                  {#if $automationRun.excludeCompanies.length > 3}
                    <Badge variant="destructive" class="text-xs"
                      >+{$automationRun.excludeCompanies.length - 3} more</Badge>
                  {/if}
                </div>
              </div>
            {/if}
          </div>
        </div>
      </div>
    </Card.Content>
  </Card.Root>

  <!-- Jobs Section -->
  <div>
    <div class="mb-4 flex items-center justify-between">
      <h2 class="text-2xl font-semibold text-gray-300">
        Jobs Found ({jobsToDisplay.length})
      </h2>
      <div class="flex items-center gap-2 text-sm text-gray-400">
        <span>Applied: {$automationRun.jobsApplied || 0}</span>
        <span>•</span>
        <span>Skipped: {$automationRun.jobsSkipped || 0}</span>
        <span>•</span>
        <span
          >Avg Match: {jobsToDisplay.length > 0
            ? (
                jobsToDisplay.reduce((sum: number, job: any) => sum + (job.matchScore || 0), 0) /
                jobsToDisplay.length
              ).toFixed(1)
            : 0}%</span>
      </div>
    </div>

    {#if jobsToDisplay.length === 0}
      <div
        class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center">
        <Briefcase class="mb-4 h-12 w-12 text-gray-400" />
        <h3 class="text-xl font-semibold text-gray-300">No jobs found yet</h3>
        <p class="mt-2 text-gray-400">
          {#if $automationRun.status === 'running'}
            The automation is still running. Jobs will appear here as they are found.
          {:else if $automationRun.status === 'pending'}
            The automation is pending. Jobs will appear here once it starts running.
          {:else}
            No jobs were found during this automation run.
          {/if}
        </p>
      </div>
    {:else}
      <!-- Auto-Apply Controls -->
      {#if !$automationRun.autoApplyEnabled}
        <div class="mb-6 rounded-lg border border-blue-500/20 bg-blue-500/5 p-4">
          <div class="mb-4 flex items-center justify-between">
            <div>
              <h3 class="text-lg font-medium text-blue-400">Enable Auto-Apply</h3>
              <p class="text-sm text-gray-400">Select jobs you want to automatically apply to</p>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-sm text-gray-400">{selectedJobsForAutoApply.size} selected</span>
            </div>
          </div>

          <div class="mb-4 flex flex-wrap gap-2">
            <Button variant="outline" size="sm" onclick={() => selectJobsByMatchScore(90)}>
              Select 90%+ Match
            </Button>
            <Button variant="outline" size="sm" onclick={() => selectJobsByMatchScore(80)}>
              Select 80%+ Match
            </Button>
            <Button variant="outline" size="sm" onclick={() => selectJobsByMatchScore(70)}>
              Select 70%+ Match
            </Button>
            <Button variant="ghost" size="sm" onclick={clearAllSelections}>Clear All</Button>
          </div>

          <div class="flex items-center gap-2">
            <Button
              onclick={showAutoApplyConfirmation}
              disabled={selectedJobsForAutoApply.size === 0}
              class="bg-blue-600 hover:bg-blue-700">
              <Target class="mr-2 h-4 w-4" />
              Enable Auto-Apply ({selectedJobsForAutoApply.size} jobs)
            </Button>
          </div>
        </div>
      {/if}

      <!-- Jobs List -->
      <div class="grid gap-4">
        {#each jobsToDisplay as job (job.id)}
          <Card.Root class="relative">
            {#if !$automationRun.autoApplyEnabled}
              <!-- Selection Checkbox -->
              <div class="absolute right-4 top-4 z-10">
                <input
                  type="checkbox"
                  checked={selectedJobsForAutoApply.has(job.id)}
                  onchange={() => toggleJobSelection(job.id)}
                  class="h-4 w-4 rounded border-gray-600 bg-gray-800 text-blue-600 focus:ring-blue-500" />
              </div>
            {/if}

            <Card.Header class="p-6 {!$automationRun.autoApplyEnabled ? 'pr-12' : ''}">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center gap-2">
                    <Card.Title class="text-lg">{job.title}</Card.Title>
                    {#if job.matchScore}
                      <Badge variant="outline" class="text-xs">
                        {job.matchScore}% match
                      </Badge>
                    {/if}
                    {#if job.applicationStatus}
                      <Badge
                        variant={job.applicationStatus === 'applied' ? 'default' : 'secondary'}
                        class="text-xs">
                        {job.applicationStatus}
                      </Badge>
                    {/if}
                    {#if $automationRun.autoApplyEnabled && $automationRun.selectedJobIds?.includes(job.id)}
                      <Badge variant="default" class="bg-blue-600 text-xs">
                        Auto-Apply Enabled
                      </Badge>
                    {/if}
                  </div>
                  <Card.Description class="mt-1">
                    <div class="flex items-center gap-1">
                      <Building class="h-3 w-3" />
                      {job.company}
                    </div>
                  </Card.Description>
                </div>
                <div class="flex items-center gap-2">
                  {#if job.applyLink || job.url}
                    <Button
                      variant="outline"
                      size="sm"
                      onclick={() => window.open(job.applyLink || job.url, '_blank')}>
                      <ExternalLink class="mr-2 h-4 w-4" />
                      Apply
                    </Button>
                  {/if}
                </div>
              </div>
            </Card.Header>
            <Card.Content class="p-6 pt-0">
              <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                {#if job.location}
                  <div>
                    <div class="flex items-center gap-1 text-sm font-medium text-gray-400">
                      <MapPin class="h-3 w-3" />
                      Location
                    </div>
                    <div class="text-sm">{job.location}</div>
                  </div>
                {/if}

                {#if job.salary || job.salaryMin || job.salaryMax}
                  <div>
                    <div class="flex items-center gap-1 text-sm font-medium text-gray-400">
                      <DollarSign class="h-3 w-3" />
                      Salary
                    </div>
                    <div class="text-sm">
                      {#if job.salary}
                        {job.salary}
                      {:else if job.salaryMin && job.salaryMax}
                        ${job.salaryMin}k - ${job.salaryMax}k
                      {:else if job.salaryMin}
                        ${job.salaryMin}k+
                      {:else if job.salaryMax}
                        Up to ${job.salaryMax}k
                      {/if}
                    </div>
                  </div>
                {/if}

                {#if job.employmentType}
                  <div>
                    <div class="text-sm font-medium text-gray-400">Type</div>
                    <div class="text-sm capitalize">{job.employmentType}</div>
                  </div>
                {/if}

                {#if job.postedDate || job.postedAt}
                  <div>
                    <div class="flex items-center gap-1 text-sm font-medium text-gray-400">
                      <Calendar class="h-3 w-3" />
                      Posted
                    </div>
                    <div class="text-sm">
                      {formatDistanceToNow(new Date(job.postedDate || job.postedAt))} ago
                    </div>
                  </div>
                {/if}
              </div>

              {#if job.description}
                <div class="mt-4">
                  <div class="text-sm font-medium text-gray-400">Description</div>
                  <div class="mt-1 line-clamp-3 text-sm">{job.description}</div>
                </div>
              {/if}

              {#if job.skills && job.skills.length > 0}
                <div class="mt-4">
                  <div class="text-sm font-medium text-gray-400">Skills</div>
                  <div class="mt-1 flex flex-wrap gap-1">
                    {#each job.skills.slice(0, 5) as skill}
                      <Badge variant="secondary" class="text-xs">{skill}</Badge>
                    {/each}
                    {#if job.skills.length > 5}
                      <Badge variant="secondary" class="text-xs"
                        >+{job.skills.length - 5} more</Badge>
                    {/if}
                  </div>
                </div>
              {/if}
            </Card.Content>
          </Card.Root>
        {/each}
      </div>
    {/if}
  </div>
</div>

<!-- Auto-Apply Confirmation Dialog -->
{#if showAutoApplyConfirm}
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
    <div class="mx-4 w-full max-w-md rounded-lg border border-gray-700 bg-gray-900 p-6">
      <h3 class="mb-4 text-lg font-semibold text-white">Confirm Auto-Apply</h3>
      <p class="mb-4 text-sm text-gray-400">
        Are you sure you want to enable auto-apply for {selectedJobsForAutoApply.size} selected job{selectedJobsForAutoApply.size ===
        1
          ? ''
          : 's'}?
      </p>
      <p class="mb-6 text-xs text-gray-500">
        This will automatically submit applications to the selected jobs using your profile and
        resume.
      </p>
      <div class="flex justify-end gap-2">
        <Button variant="ghost" onclick={() => (showAutoApplyConfirm = false)}>Cancel</Button>
        <Button onclick={confirmAutoApply} class="bg-blue-600 hover:bg-blue-700">
          Enable Auto-Apply
        </Button>
      </div>
    </div>
  </div>
{/if}
