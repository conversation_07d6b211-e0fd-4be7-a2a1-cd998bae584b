import { json } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import { getUserFromToken } from '$lib/server/auth.js';
import type { RequestHandler } from './$types';

// Update automation run settings
export const PUT: RequestHandler = async ({ params, request, cookies }) => {
  const user = getUserFromToken(cookies);
  const { id } = params;

  if (!user) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Check if the automation run exists and belongs to the user
    const automationRun = await prisma.automationRun.findFirst({
      where: {
        id,
        OR: [
          { userId: user.id },
          {
            profile: {
              team: {
                members: {
                  some: { userId: user.id },
                },
              },
            },
          },
        ],
      },
    });

    if (!automationRun) {
      return json({ error: 'Automation run not found' }, { status: 404 });
    }

    // Parse the request body
    const {
      autoApplyEnabled,
      maxJobsToApply,
      minMatchScore,
    } = await request.json();

    // Update the automation run settings
    const updatedRun = await prisma.automationRun.update({
      where: { id },
      data: {
        autoApplyEnabled: autoApplyEnabled !== undefined ? autoApplyEnabled : automationRun.autoApplyEnabled,
        maxJobsToApply: maxJobsToApply !== undefined ? maxJobsToApply : automationRun.maxJobsToApply,
        minMatchScore: minMatchScore !== undefined ? minMatchScore : automationRun.minMatchScore,
        updatedAt: new Date(),
      },
      include: {
        profile: {
          include: {
            data: true,
            resumes: {
              include: {
                document: true
              }
            }
          }
        },
        jobs: {
          orderBy: {
            createdAt: 'desc'
          }
        },
      },
    });

    return json(updatedRun);
  } catch (error) {
    console.error('Error updating automation run settings:', error);
    return json({ error: 'Failed to update automation run settings' }, { status: 500 });
  }
};
