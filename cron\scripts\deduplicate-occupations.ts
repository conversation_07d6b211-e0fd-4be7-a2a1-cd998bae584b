// cron/scripts/deduplicate-occupations.ts
import { PrismaClient } from "@prisma/client";
import { logger } from "../utils/logger";

const prisma = new PrismaClient();

interface OccupationGroup {
  title: string;
  occupations: {
    id: string;
    socCode: string;
    title: string;
    shortTitle: string | null;
    category: string;
    source: string | null;
    createdAt: Date;
  }[];
}

async function deduplicateOccupations() {
  logger.info("🔍 Starting occupation deduplication...");

  try {
    // Get all occupations
    const allOccupations = await prisma.occupations.findMany({
      orderBy: [
        { title: "asc" },
        { createdAt: "asc" }, // Keep the oldest one
      ],
    });

    logger.info(`📊 Found ${allOccupations.length} total occupations`);

    // Function to normalize titles for better duplicate detection
    function normalizeTitle(title: string): string {
      let normalized = title.toLowerCase().trim();

      // Remove common parenthetical explanations
      normalized = normalized.replace(/\s*\([^)]*\)\s*/g, " ");

      // Replace common abbreviations with full forms
      const abbreviations: Record<string, string> = {
        "a/c": "air conditioning",
        ac: "air conditioning",
        hvac: "heating ventilation air conditioning",
        "3d": "three dimensional",
        it: "information technology",
        hr: "human resources",
        qa: "quality assurance",
        qc: "quality control",
        "r&d": "research and development",
        ceo: "chief executive officer",
        cto: "chief technology officer",
        cfo: "chief financial officer",
        vp: "vice president",
        mgr: "manager",
        mgmt: "management",
        admin: "administrator",
        tech: "technician",
        dev: "developer",
        eng: "engineer",
        sr: "senior",
        jr: "junior",
        asst: "assistant",
        assoc: "associate",
        coord: "coordinator",
        spec: "specialist",
      };

      // Replace abbreviations (word boundaries to avoid partial matches)
      for (const [abbrev, full] of Object.entries(abbreviations)) {
        const regex = new RegExp(
          `\\b${abbrev.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\b`,
          "gi"
        );
        normalized = normalized.replace(regex, full);
      }

      // Remove extra whitespace and normalize
      normalized = normalized.replace(/\s+/g, " ").trim();

      return normalized;
    }

    // Group by normalized title
    const groupedByTitle = new Map<string, OccupationGroup>();

    for (const occupation of allOccupations) {
      const normalizedTitle = normalizeTitle(occupation.title);

      if (!groupedByTitle.has(normalizedTitle)) {
        groupedByTitle.set(normalizedTitle, {
          title: normalizedTitle,
          occupations: [],
        });
      }

      groupedByTitle.get(normalizedTitle)!.occupations.push(occupation);
    }

    // Find duplicates
    const duplicateGroups = Array.from(groupedByTitle.values()).filter(
      (group) => group.occupations.length > 1
    );

    logger.info(`🔍 Found ${duplicateGroups.length} groups with duplicates`);

    let totalDuplicatesRemoved = 0;

    // Process each duplicate group
    for (const group of duplicateGroups) {
      logger.info(
        `📝 Processing duplicates for: "${group.occupations[0].title}"`
      );
      logger.info(`   Found ${group.occupations.length} duplicates`);

      // Sort by priority: prefer entries with socCode, then by creation date
      const sorted = group.occupations.slice().sort((a, b) => {
        // Prefer entries with socCode
        if (a.socCode && !b.socCode) return -1;
        if (!a.socCode && b.socCode) return 1;

        // If both have or don't have socCode, prefer older entries
        return a.createdAt.getTime() - b.createdAt.getTime();
      });

      // Keep the first one (highest priority)
      const toKeep = sorted[0];
      const toDelete = sorted.slice(1);

      logger.info(
        `   ✅ Keeping: ${toKeep.id} (socCode: ${toKeep.socCode || "none"}, created: ${toKeep.createdAt.toISOString()})`
      );

      // Check if any of the duplicates have related data
      for (const duplicate of toDelete) {
        // Check for JobMarketMetrics
        const hasMetrics = await prisma.jobMarketMetrics.count({
          where: { occupationId: duplicate.id },
        });

        // Check for SkillTrend
        const hasSkillTrends = await prisma.skillTrend.count({
          where: { occupationId: duplicate.id },
        });

        if (hasMetrics > 0 || hasSkillTrends > 0) {
          logger.info(
            `⚠️  Duplicate ${duplicate.id} has related data (metrics: ${hasMetrics}, skills: ${hasSkillTrends})`
          );
          logger.info(`🔄 Moving related data to kept record...`);

          // Move JobMarketMetrics
          if (hasMetrics > 0) {
            await prisma.jobMarketMetrics.updateMany({
              where: { occupationId: duplicate.id },
              data: { occupationId: toKeep.id },
            });
          }

          // Move SkillTrend
          if (hasSkillTrends > 0) {
            await prisma.skillTrend.updateMany({
              where: { occupationId: duplicate.id },
              data: { occupationId: toKeep.id },
            });
          }
        }

        logger.info(
          `   🗑️  Deleting: ${duplicate.id} (socCode: ${duplicate.socCode || "none"})`
        );
      }

      // Delete the duplicates
      const idsToDelete = toDelete.map((d) => d.id);
      await prisma.occupations.deleteMany({
        where: {
          id: {
            in: idsToDelete,
          },
        },
      });

      totalDuplicatesRemoved += idsToDelete.length;
      logger.info(`   ✅ Deleted ${idsToDelete.length} duplicates`);
    }

    logger.info(`🎉 Deduplication complete!`);
    logger.info(`📊 Total duplicates removed: ${totalDuplicatesRemoved}`);
    logger.info(
      `📊 Remaining occupations: ${allOccupations.length - totalDuplicatesRemoved}`
    );

    // Verify no duplicates remain
    const remainingOccupations = await prisma.occupations.findMany({
      select: { title: true },
    });

    const titleCounts = new Map<string, number>();
    for (const occ of remainingOccupations) {
      const normalizedTitle = normalizeTitle(occ.title);
      titleCounts.set(
        normalizedTitle,
        (titleCounts.get(normalizedTitle) ?? 0) + 1
      );
    }

    const stillDuplicated = Array.from(titleCounts.entries()).filter(
      ([_, count]) => count > 1
    );

    if (stillDuplicated.length > 0) {
      logger.warn(
        `⚠️  Warning: ${stillDuplicated.length} titles still have duplicates:`
      );
      stillDuplicated.forEach(([title, count]) => {
        logger.warn(`   - "${title}": ${count} entries`);
      });
    } else {
      logger.info(`✅ Verification complete: No duplicates remain`);
    }
  } catch (error) {
    logger.error("❌ Error during deduplication:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
deduplicateOccupations()
  .then(() => {
    logger.info("✅ Script completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("❌ Script failed:", error);
    process.exit(1);
  });

export { deduplicateOccupations };
