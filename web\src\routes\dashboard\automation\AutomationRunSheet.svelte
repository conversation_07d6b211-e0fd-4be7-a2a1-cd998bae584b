<script lang="ts">
  import { toast } from 'svelte-sonner';
  import { writable } from 'svelte/store';
  import * as Sheet from '$lib/components/ui/sheet';
  import { Button } from '$lib/components/ui/button';
  import { Badge } from '$lib/components/ui/badge';
  import { Progress } from '$lib/components/ui/progress/index.js';
  import { Switch } from '$lib/components/ui/switch';
  import * as AlertDialog from '$lib/components/ui/alert-dialog';

  import { formatDistanceToNow } from '$lib/utils';
  import {
    Play,
    StopCircle,
    RefreshCw,
    FileText,
    Briefcase,
    CheckCircle,
    XCircle,
    Clock,
    Building,
    MapPin,
    DollarSign,
    Calendar,
    Target,
  } from 'lucide-svelte';

  // Props
  const {
    open = $bindable(false),
    automationRun,
    onClose = () => {},
    onRefresh = () => {},
    onStop = () => {},
  } = $props<{
    open?: boolean;
    automationRun: any;
    onClose?: () => void;
    onRefresh?: (updatedRun?: any) => void;
    onStop?: (runId?: string) => void;
  }>();

  // Local state
  let isLoading = $state(false);
  let isStoppingRun = $state(false);
  let selectedJobsForAutoApply = $state(new Set<string>());
  let showAutoApplyConfirm = $state(false);

  // Mock jobs data for UX testing
  const mockJobs = [
    {
      id: '1',
      title: 'Senior Frontend Developer',
      company: 'TechCorp Inc.',
      location: 'San Francisco, CA',
      salary: '$120k - $160k',
      matchScore: 92,
      applyLink: 'https://example.com/apply/1',
      postedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      skills: ['React', 'TypeScript', 'JavaScript'],
    },
    {
      id: '2',
      title: 'Full Stack Engineer',
      company: 'StartupXYZ',
      location: 'Remote',
      salary: '$100k - $140k',
      matchScore: 88,
      applyLink: 'https://example.com/apply/2',
      postedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
      skills: ['React', 'Node.js', 'PostgreSQL'],
    },
    {
      id: '3',
      title: 'React Developer',
      company: 'Digital Agency Co.',
      location: 'New York, NY',
      salary: '$90k - $120k',
      matchScore: 85,
      applyLink: 'https://example.com/apply/3',
      postedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      skills: ['React', 'Redux', 'TypeScript'],
    },
  ];

  // Reactive automationRun store
  const runStore = writable(automationRun);
  $effect(() => {
    if (automationRun) {
      runStore.set(automationRun);
    }
  });

  // Use mock jobs for UI testing, fallback to real data
  const jobsToDisplay = mockJobs.length > 0 ? mockJobs : $runStore?.jobs || [];

  // Function to toggle job selection for auto-apply
  function toggleJobSelection(jobId: string) {
    if (selectedJobsForAutoApply.has(jobId)) {
      selectedJobsForAutoApply.delete(jobId);
    } else {
      selectedJobsForAutoApply.add(jobId);
    }
    selectedJobsForAutoApply = new Set(selectedJobsForAutoApply);
  }

  // Function to show auto-apply confirmation
  function showAutoApplyConfirmation() {
    if (selectedJobsForAutoApply.size === 0) {
      toast.error('Please select at least one job to enable auto-apply');
      return;
    }
    showAutoApplyConfirm = true;
  }

  // Function to confirm and enable auto-apply
  async function confirmAutoApply() {
    try {
      const selectedJobs = Array.from(selectedJobsForAutoApply);
      console.log('Enabling auto-apply for jobs:', selectedJobs);

      toast.success(
        `Auto-apply enabled for ${selectedJobs.length} job${selectedJobs.length === 1 ? '' : 's'}`
      );
      showAutoApplyConfirm = false;

      runStore.update((run) => ({
        ...run,
        autoApplyEnabled: true,
        selectedJobIds: selectedJobs,
      }));
    } catch (error) {
      console.error('Error enabling auto-apply:', error);
      toast.error('Failed to enable auto-apply');
    }
  }

  // Function to stop an automation run
  async function stopAutomationRun() {
    if (!$runStore || !$runStore.id) return;

    isStoppingRun = true;

    try {
      const response = await fetch(`/api/automation/runs/${$runStore.id}/stop`, {
        method: 'POST',
      });

      if (response.ok) {
        const updatedRun = await response.json();
        runStore.update((run) => ({
          ...run,
          status: 'stopped',
          stoppedAt: updatedRun.stoppedAt,
        }));
        toast.success('Automation run stopped');
        onStop($runStore.id);
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to stop automation run');
      }
    } catch (error) {
      console.error('Error stopping automation run:', error);
      toast.error('An error occurred while stopping the automation run');
    } finally {
      isStoppingRun = false;
    }
  }

  // Function to refresh the automation run data
  async function refreshData() {
    if (!$runStore || !$runStore.id) return;

    isLoading = true;

    try {
      const response = await fetch(`/api/automation/runs/${$runStore.id}`);
      if (response.ok) {
        const updatedRun = await response.json();
        runStore.set(updatedRun);
        toast.success('Data refreshed');
        onRefresh(updatedRun);
      } else {
        toast.error('Failed to refresh data');
      }
    } catch (error) {
      console.error('Error refreshing data:', error);
      toast.error('An error occurred while refreshing data');
    } finally {
      isLoading = false;
    }
  }

  // Function to get status badge variant
  function getStatusVariant(status: string) {
    switch (status) {
      case 'running':
        return 'default';
      case 'completed':
        return 'outline';
      case 'failed':
        return 'destructive';
      case 'stopped':
        return 'secondary';
      default:
        return 'secondary';
    }
  }

  // Function to get status icon
  function getStatusIcon(status: string) {
    switch (status) {
      case 'running':
        return Play;
      case 'completed':
        return CheckCircle;
      case 'failed':
        return XCircle;
      case 'stopped':
        return StopCircle;
      case 'pending':
        return Clock;
      default:
        return Clock;
    }
  }

  // Function to calculate progress percentage
  function calculateProgress(run: any) {
    if (!run) return 0;
    if (run.status === 'completed') return 100;
    if (run.status === 'failed' || run.status === 'stopped') return run.progress || 0;
    return run.progress || 0;
  }

  // Type definition for profile data
  type ProfileData = {
    fullName?: string;
    title?: string;
    headline?: string;
    skills?: string[];
  };

  // Helper function to safely access profile data
  function getProfileData(profile: any): ProfileData {
    if (!profile?.data) return {};

    try {
      // Try to parse the data if it's a string
      if (typeof profile.data === 'string') {
        return JSON.parse(profile.data);
      }

      // Otherwise, return the data object
      return profile.data as ProfileData;
    } catch (e) {
      console.error('Error parsing profile data:', e);
      return {};
    }
  }

  // Handle sheet close
  function handleSheetClose() {
    onClose();
  }
</script>

<Sheet.Root {open} onOpenChange={handleSheetClose}>
  <Sheet.Trigger />
  <Sheet.Portal>
    <Sheet.Overlay />
    <Sheet.Content side="right" class="w-full sm:max-w-xl md:max-w-2xl lg:max-w-3xl">
      <Sheet.Header>
        <Sheet.Title>Automation Run Details</Sheet.Title>
        <Sheet.Description>View details and progress of your automation run</Sheet.Description>
      </Sheet.Header>

      {#if $runStore}
        <div class="mt-6 space-y-6">
          <!-- Status and Actions -->
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <Badge variant={getStatusVariant($runStore.status)} class="text-sm">
                {@const StatusIcon = getStatusIcon($runStore.status)}
                <StatusIcon class="mr-1 h-3 w-3" />
                {$runStore.status.charAt(0).toUpperCase() + $runStore.status.slice(1)}
              </Badge>

              {#if $runStore.createdAt}
                <span class="text-xs text-gray-400">
                  Started {formatDistanceToNow(new Date($runStore.createdAt))} ago
                </span>
              {/if}
            </div>

            <div class="flex items-center gap-2">
              {#if $runStore.status === 'running' || $runStore.status === 'pending'}
                <Button
                  variant="outline"
                  size="sm"
                  onclick={stopAutomationRun}
                  disabled={isStoppingRun}>
                  <StopCircle class="mr-2 h-4 w-4" />
                  {isStoppingRun ? 'Stopping...' : 'Stop Run'}
                </Button>
              {/if}

              <Button variant="outline" size="sm" onclick={refreshData} disabled={isLoading}>
                <RefreshCw class="mr-2 h-4 w-4 {isLoading ? 'animate-spin' : ''}" />
                {isLoading ? 'Refreshing...' : 'Refresh'}
              </Button>
            </div>
          </div>

          <!-- Progress Bar -->
          <div>
            <Progress value={calculateProgress($runStore)} max={100} />
            <div class="mt-1 text-right text-xs text-gray-400">
              {calculateProgress($runStore).toFixed(0)}% Complete
            </div>
          </div>

          <!-- Run Information -->
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <!-- Profile Info -->
            <div class="rounded-lg border p-4">
              <h3 class="mb-2 text-sm font-medium text-gray-400">Profile</h3>
              {#if $runStore.profile}
                <div class="mb-1 text-base font-medium">
                  {getProfileData($runStore.profile).fullName || 'Unnamed Profile'}
                </div>
                <div class="mb-2 text-sm text-gray-400">
                  {getProfileData($runStore.profile).title ||
                    getProfileData($runStore.profile).headline ||
                    'No title specified'}
                </div>

                {#if $runStore.profile.documents && $runStore.profile.documents.length > 0}
                  <Badge variant="outline" class="text-xs">
                    <FileText class="mr-1 h-3 w-3" />
                    {$runStore.profile.documents.length}
                    {$runStore.profile.documents.length === 1 ? 'resume' : 'resumes'}
                  </Badge>
                {/if}
              {:else}
                <div class="text-sm text-gray-400">Profile information not available</div>
              {/if}
            </div>

            <!-- Search Parameters -->
            <div class="rounded-lg border p-4">
              <h3 class="mb-2 text-sm font-medium text-gray-400">Search Parameters</h3>
              <div class="space-y-2">
                <div>
                  <span class="text-xs text-gray-400">Keywords:</span>
                  <div class="text-sm">{$runStore.keywords || 'None specified'}</div>
                </div>

                <div>
                  <span class="text-xs text-gray-400">Location:</span>
                  <div class="text-sm">{$runStore.location || 'None specified'}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Jobs Found -->
          <div>
            <div class="mb-4 flex items-center justify-between">
              <h3 class="text-base font-medium">
                Jobs Found ({jobsToDisplay.length})
              </h3>
              {#if jobsToDisplay.length > 0}
                <Button
                  variant="outline"
                  size="sm"
                  onclick={showAutoApplyConfirmation}
                  disabled={selectedJobsForAutoApply.size === 0}
                  class="h-8 text-xs">
                  <Target class="mr-1 h-3 w-3" />
                  Apply Selected ({selectedJobsForAutoApply.size})
                </Button>
              {/if}
            </div>

            {#if jobsToDisplay.length === 0}
              <div
                class="flex flex-col items-center justify-center rounded-lg border border-dashed p-6 text-center">
                <Briefcase class="mb-2 h-8 w-8 text-gray-400" />
                <p class="text-sm text-gray-400">
                  {#if $runStore.status === 'running' || $runStore.status === 'pending'}
                    Jobs will appear here as they are found
                  {:else}
                    No jobs were found during this automation run
                  {/if}
                </p>
              </div>
            {:else}
              <!-- Auto-Apply Controls -->
              {#if !$runStore.autoApplyEnabled}
                <div class="mb-4 rounded-lg border border-blue-500/20 bg-blue-500/5 p-3">
                  <div class="mb-2 flex items-center justify-between">
                    <div>
                      <h4 class="text-sm font-medium text-blue-400">Enable Auto-Apply</h4>
                      <p class="text-xs text-gray-400">Select jobs to automatically apply to</p>
                    </div>
                    <span class="text-xs text-gray-400"
                      >{selectedJobsForAutoApply.size} selected</span>
                  </div>

                  <Button
                    onclick={showAutoApplyConfirmation}
                    disabled={selectedJobsForAutoApply.size === 0}
                    size="sm"
                    class="w-full bg-blue-600 hover:bg-blue-700">
                    <Target class="mr-2 h-3 w-3" />
                    Enable Auto-Apply ({selectedJobsForAutoApply.size} jobs)
                  </Button>
                </div>
              {/if}

              <div class="max-h-60 space-y-3 overflow-y-auto pr-1">
                {#each jobsToDisplay as job (job.id)}
                  <div class="flex flex-col space-y-3 rounded-lg border p-4">
                    <!-- Job Header with Switch -->
                    <div class="flex items-start justify-between">
                      <div class="min-w-0 flex-1">
                        <div class="flex items-center gap-2">
                          <div class="font-medium">{job.title}</div>
                          {#if job.matchScore}
                            <Badge variant="outline" class="text-xs">
                              {job.matchScore}% match
                            </Badge>
                          {/if}
                          {#if $runStore.autoApplyEnabled && $runStore.selectedJobIds?.includes(job.id)}
                            <Badge variant="default" class="bg-blue-600 text-xs">Auto-Apply</Badge>
                          {/if}
                        </div>
                        {#if job.company}
                          <div class="flex items-center text-sm text-gray-400">
                            <Building class="mr-1 h-3 w-3" />
                            {job.company}
                          </div>
                        {/if}
                      </div>

                      <div class="flex items-center gap-2">
                        {#if !$runStore.autoApplyEnabled}
                          <Switch
                            checked={selectedJobsForAutoApply.has(job.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                toggleJobSelection(job.id);
                              } else {
                                toggleJobSelection(job.id);
                              }
                            }} />
                        {/if}
                        {#if job.applyLink}
                          <Button
                            variant="outline"
                            size="sm"
                            onclick={() => window.open(job.applyLink, '_blank')}>
                            Apply
                          </Button>
                        {/if}
                      </div>
                    </div>

                    <!-- Job Details -->
                    <div class="grid grid-cols-2 gap-2 text-xs">
                      {#if job.location}
                        <div class="flex items-center text-gray-400">
                          <MapPin class="mr-1 h-3 w-3" />
                          {job.location}
                        </div>
                      {/if}

                      {#if job.salary}
                        <div class="flex items-center text-gray-400">
                          <DollarSign class="mr-1 h-3 w-3" />
                          {job.salary}
                        </div>
                      {/if}

                      {#if job.postedAt}
                        <div class="flex items-center text-gray-400">
                          <Calendar class="mr-1 h-3 w-3" />
                          Posted {formatDistanceToNow(new Date(job.postedAt))} ago
                        </div>
                      {/if}
                    </div>

                    <!-- Skills -->
                    {#if job.skills && job.skills.length > 0}
                      <div class="flex flex-wrap gap-1">
                        {#each job.skills.slice(0, 3) as skill}
                          <Badge variant="secondary" class="text-xs">{skill}</Badge>
                        {/each}
                        {#if job.skills.length > 3}
                          <Badge variant="secondary" class="text-xs"
                            >+{job.skills.length - 3}</Badge>
                        {/if}
                      </div>
                    {/if}
                  </div>
                {/each}
              </div>
            {/if}
          </div>
        </div>
      {:else}
        <div class="flex h-40 items-center justify-center">
          <p class="text-gray-400">No automation run data available</p>
        </div>
      {/if}

      <Sheet.Footer class="mt-6">
        <Button variant="outline" onclick={handleSheetClose}>Close</Button>
        <Button
          variant="default"
          onclick={() => {
            handleSheetClose();
            if ($runStore && $runStore.id) {
              window.location.href = `/dashboard/automation/${$runStore.id}`;
            }
          }}>
          View Full Details
        </Button>
      </Sheet.Footer>
    </Sheet.Content>
  </Sheet.Portal>
</Sheet.Root>

<!-- Auto-Apply Confirmation Dialog -->
<AlertDialog.Root bind:open={showAutoApplyConfirm}>
  <AlertDialog.Content>
    <AlertDialog.Header>
      <AlertDialog.Title>Confirm Auto-Apply</AlertDialog.Title>
      <AlertDialog.Description>
        Are you sure you want to enable auto-apply for {selectedJobsForAutoApply.size} selected job{selectedJobsForAutoApply.size ===
        1
          ? ''
          : 's'}?
        <br /><br />
        This will automatically submit applications to the selected jobs using your profile and resume.
      </AlertDialog.Description>
    </AlertDialog.Header>
    <AlertDialog.Footer>
      <AlertDialog.Cancel onclick={() => (showAutoApplyConfirm = false)}>Cancel</AlertDialog.Cancel>
      <AlertDialog.Action onclick={confirmAutoApply}>
        <Target class="mr-2 h-4 w-4" />
        Enable Auto-Apply
      </AlertDialog.Action>
    </AlertDialog.Footer>
  </AlertDialog.Content>
</AlertDialog.Root>
