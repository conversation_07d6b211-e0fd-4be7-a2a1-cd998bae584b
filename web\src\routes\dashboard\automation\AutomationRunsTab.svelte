<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input/index.js';
  import * as Card from '$lib/components/ui/card';
  import * as Select from '$lib/components/ui/select';
  import { Badge } from '$lib/components/ui/badge';
  import { Progress } from '$lib/components/ui/progress/index.js';
  import { Switch } from '$lib/components/ui/switch';
  import * as AlertDialog from '$lib/components/ui/alert-dialog';
  import FeatureGuard from '$components/features/EnhancedFeatureGuard.svelte';
  import { getProfileData } from '$lib/utils/profile';
  import { formatDistance } from 'date-fns';
  import { toast } from 'svelte-sonner';
  import {
    Search,
    Play,
    ExternalLink,
    CheckCircle,
    Clock,
    XCircle,
    StopCircle,
    Target,
  } from 'lucide-svelte';

  const { userData, automationRuns, onCreateRun } = $props<{
    userData: any;
    automationRuns: any;
    onCreateRun: () => void;
  }>();

  // Filter and search state
  let runStatusFilter = $state('all');
  let runSearchQuery = $state('');

  // Auto-apply state
  let selectedJobsPerRun = $state<Record<string, Set<string>>>({});
  let showApplyConfirmDialog = $state(false);
  let currentRunForApply = $state<any>(null);

  // Mock jobs data for each run (for UX development)
  const mockJobsData: Record<string, any[]> = {
    // This would normally come from the backend
  };

  // Function to get jobs for a run (mock data for now)
  function getJobsForRun(runId: string) {
    return (
      mockJobsData[runId] || [
        {
          id: `${runId}-job-1`,
          title: 'Senior Frontend Developer',
          company: 'TechCorp Inc.',
          location: 'San Francisco, CA',
          salary: '$120k - $160k',
          matchScore: 92,
        },
        {
          id: `${runId}-job-2`,
          title: 'Full Stack Engineer',
          company: 'StartupXYZ',
          location: 'Remote',
          salary: '$100k - $140k',
          matchScore: 88,
        },
        {
          id: `${runId}-job-3`,
          title: 'React Developer',
          company: 'Digital Agency Co.',
          location: 'New York, NY',
          salary: '$90k - $120k',
          matchScore: 85,
        },
      ]
    );
  }

  // Function to toggle job selection for auto-apply
  function toggleJobSelection(runId: string, jobId: string) {
    if (!selectedJobsPerRun[runId]) {
      selectedJobsPerRun[runId] = new Set();
    }

    if (selectedJobsPerRun[runId].has(jobId)) {
      selectedJobsPerRun[runId].delete(jobId);
    } else {
      selectedJobsPerRun[runId].add(jobId);
    }

    // Trigger reactivity
    selectedJobsPerRun = { ...selectedJobsPerRun };
  }

  // Function to show apply confirmation
  function showApplyConfirmation(run: any) {
    const selectedJobs = selectedJobsPerRun[run.id];
    if (!selectedJobs || selectedJobs.size === 0) {
      toast.error('Please select at least one job to apply to');
      return;
    }
    currentRunForApply = run;
    showApplyConfirmDialog = true;
  }

  // Function to confirm auto-apply
  async function confirmAutoApply() {
    if (!currentRunForApply) return;

    try {
      const selectedJobs = Array.from(selectedJobsPerRun[currentRunForApply.id] || []);
      console.log('Applying to jobs:', selectedJobs);

      toast.success(
        `Auto-apply enabled for ${selectedJobs.length} job${selectedJobs.length === 1 ? '' : 's'}`
      );

      showApplyConfirmDialog = false;
      currentRunForApply = null;

      // Clear selections for this run
      if (selectedJobsPerRun[currentRunForApply.id]) {
        selectedJobsPerRun[currentRunForApply.id].clear();
        selectedJobsPerRun = { ...selectedJobsPerRun };
      }
    } catch (error) {
      console.error('Error enabling auto-apply:', error);
      toast.error('Failed to enable auto-apply');
    }
  }

  // Status filter options
  const statusFilterOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'pending', label: 'Pending' },
    { value: 'running', label: 'Running' },
    { value: 'completed', label: 'Completed' },
    { value: 'failed', label: 'Failed' },
    { value: 'stopped', label: 'Stopped' },
  ];

  // Get current status filter option
  const currentStatusOption = $derived(() => {
    return (
      statusFilterOptions.find((option) => option.value === runStatusFilter) ||
      statusFilterOptions[0]
    );
  });

  // Filtered automation runs
  const filteredAutomationRuns = $derived(() => {
    return $automationRuns.filter((run) => {
      // Status filter
      if (runStatusFilter !== 'all' && run.status !== runStatusFilter) {
        return false;
      }

      // Search filter
      if (runSearchQuery.trim()) {
        const query = runSearchQuery.toLowerCase();
        const profileName = run.profile ? getProfileData(run.profile).fullName || '' : '';
        const keywords = run.keywords || '';
        const location = run.location || '';

        return (
          profileName.toLowerCase().includes(query) ||
          keywords.toLowerCase().includes(query) ||
          location.toLowerCase().includes(query)
        );
      }

      return true;
    });
  });

  // Helper functions
  function formatDistanceToNow(date: Date | string): string {
    if (!date) return '';
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return formatDistance(dateObj, new Date(), { addSuffix: true });
  }

  function getStatusBadgeVariant(status: string) {
    switch (status) {
      case 'completed':
        return 'default';
      case 'running':
        return 'secondary';
      case 'failed':
        return 'destructive';
      case 'stopped':
        return 'outline';
      default:
        return 'outline';
    }
  }

  function getStatusIcon(status: string) {
    switch (status) {
      case 'completed':
        return CheckCircle;
      case 'running':
        return Play;
      case 'failed':
        return XCircle;
      case 'stopped':
        return StopCircle;
      default:
        return Clock;
    }
  }

  function calculateProgress(run: any): number {
    if (run.status === 'completed') return 100;
    if (run.status === 'failed' || run.status === 'stopped') return 0;
    if (run.status === 'running') return 50;
    return 0;
  }
</script>

<FeatureGuard
  {userData}
  featureId="automation"
  limitId="automation_runs_per_month"
  showUpgradePrompt={true}
  fallbackMessage="Automation features are not available in your current plan">
  <div class="mb-6 flex flex-wrap items-center justify-between gap-4">
    <div class="flex flex-wrap items-center gap-2">
      <Select.Root
        type="single"
        value={runStatusFilter}
        onValueChange={(value) => {
          runStatusFilter = value || 'all';
        }}>
        <Select.Trigger class="w-[140px] p-2">
          <Select.Value placeholder={currentStatusOption().label} />
        </Select.Trigger>
        <Select.Content class="w-[140px]">
          {#each statusFilterOptions as option (option.value)}
            <Select.Item value={option.value}>
              {option.label}
            </Select.Item>
          {/each}
        </Select.Content>
      </Select.Root>
    </div>
    <div class="relative flex items-center gap-2">
      <Search class="text-muted-foreground absolute left-2.5 top-3 h-4 w-4" />
      <Input placeholder="Search runs..." class="h-10 w-[200px] pl-9" bind:value={runSearchQuery} />
    </div>
  </div>

  {#if $automationRuns.length === 0}
    <div
      class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center">
      <Search class="mb-4 h-12 w-12 text-gray-400" />
      <h3 class="text-xl font-semibold text-gray-300">No automation runs yet</h3>
      <p class="mt-2 text-gray-400">Create your first automation run to start searching for jobs</p>
      <Button variant="default" onclick={onCreateRun} class="mt-4">
        <Play class="mr-2 h-4 w-4" />
        New Automation Run
      </Button>
    </div>
  {:else if filteredAutomationRuns().length === 0}
    <div
      class="flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-600 p-12 text-center">
      <Search class="mb-4 h-12 w-12 text-gray-400" />
      <h3 class="text-xl font-semibold text-gray-300">No runs match your filters</h3>
      <p class="mt-2 text-gray-400">Try adjusting your search or filter criteria</p>
    </div>
  {:else}
    <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {#each filteredAutomationRuns() as run (run.id)}
        <Card.Root class="overflow-hidden">
          <Card.Header class="p-4 pb-0">
            <div class="flex items-center justify-between">
              <Card.Title>
                {#if run.profile}
                  {getProfileData(run.profile).fullName || 'Unnamed Profile'}
                {:else}
                  Automation Run
                {/if}
              </Card.Title>
              <Badge variant={getStatusBadgeVariant(run.status)} class="ml-2">
                {#if getStatusIcon(run.status)}
                  {@const Icon = getStatusIcon(run.status)}
                  <Icon class="mr-1 h-3 w-3" />
                {/if}
                {run.status.charAt(0).toUpperCase() + run.status.slice(1)}
              </Badge>
            </div>
            <Card.Description>
              {#if run.createdAt}
                Started {formatDistanceToNow(new Date(run.createdAt))} ago
              {/if}
            </Card.Description>
          </Card.Header>

          <Card.Content class="p-4">
            <div class="mb-4">
              <div class="mb-1 text-sm font-medium text-gray-400">Progress</div>
              <Progress value={calculateProgress(run)} max={100} />
              <div class="mt-1 text-right text-xs text-gray-400">
                {calculateProgress(run)}% Complete
              </div>
            </div>

            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div class="font-medium text-gray-400">Keywords</div>
                <div class="truncate">{run.keywords || 'None'}</div>
              </div>
              <div>
                <div class="font-medium text-gray-400">Location</div>
                <div class="truncate">{run.location || 'None'}</div>
              </div>
            </div>

            <div class="mt-4">
              <div class="mb-2 flex items-center justify-between">
                <div class="font-medium text-gray-400">Jobs Found</div>
                {#if getJobsForRun(run.id).length > 0}
                  <Button
                    variant="outline"
                    size="sm"
                    onclick={() => showApplyConfirmation(run)}
                    disabled={!selectedJobsPerRun[run.id] || selectedJobsPerRun[run.id].size === 0}
                    class="h-7 text-xs">
                    <Target class="mr-1 h-3 w-3" />
                    Apply Selected ({selectedJobsPerRun[run.id]?.size || 0})
                  </Button>
                {/if}
              </div>
              <div class="flex items-center gap-2">
                <span class="text-lg font-semibold">{getJobsForRun(run.id).length}</span>
                {#if run.status === 'running' || run.status === 'pending'}
                  <span class="text-xs text-gray-400">(in progress)</span>
                {/if}
              </div>

              <!-- Job List with Switches -->
              {#if getJobsForRun(run.id).length > 0}
                <div class="mt-3 max-h-32 space-y-2 overflow-y-auto">
                  {#each getJobsForRun(run.id) as job (job.id)}
                    <div class="flex flex-col space-y-2 rounded border p-3 text-sm">
                      <div class="flex items-start justify-between">
                        <div class="min-w-0 flex-1">
                          <div class="truncate font-medium">{job.title}</div>
                          <div class="truncate text-xs text-gray-500">
                            {job.company} • {job.location}
                          </div>
                          {#if job.matchScore}
                            <div class="text-xs text-blue-400">{job.matchScore}% match</div>
                          {/if}
                        </div>
                        <Switch
                          checked={selectedJobsPerRun[run.id]?.has(job.id) || false}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              toggleJobSelection(run.id, job.id);
                            } else {
                              toggleJobSelection(run.id, job.id);
                            }
                          }}
                          class="ml-2" />
                      </div>
                    </div>
                  {/each}
                </div>
              {/if}
            </div>
          </Card.Content>

          <Card.Footer class="border-t p-4">
            <div class="flex justify-end">
              <Button
                variant="outline"
                size="sm"
                onclick={() => (window.location.href = `/dashboard/automation/${run.id}`)}>
                <ExternalLink class="mr-2 h-4 w-4" />
                View Details
              </Button>
            </div>
          </Card.Footer>
        </Card.Root>
      {/each}
    </div>
  {/if}
</FeatureGuard>

<!-- Apply Selected Confirmation Dialog -->
<AlertDialog.Root bind:open={showApplyConfirmDialog}>
  <AlertDialog.Content>
    <AlertDialog.Header>
      <AlertDialog.Title>Confirm Auto-Apply</AlertDialog.Title>
      <AlertDialog.Description>
        Are you sure you want to enable auto-apply for {selectedJobsPerRun[currentRunForApply?.id]
          ?.size || 0} selected job{(selectedJobsPerRun[currentRunForApply?.id]?.size || 0) === 1
          ? ''
          : 's'}?
        <br /><br />
        This will automatically submit applications to the selected jobs using your profile and resume.
      </AlertDialog.Description>
    </AlertDialog.Header>
    <AlertDialog.Footer>
      <AlertDialog.Cancel onclick={() => (showApplyConfirmDialog = false)}
        >Cancel</AlertDialog.Cancel>
      <AlertDialog.Action onclick={confirmAutoApply}>
        <Target class="mr-2 h-4 w-4" />
        Enable Auto-Apply
      </AlertDialog.Action>
    </AlertDialog.Footer>
  </AlertDialog.Content>
</AlertDialog.Root>
