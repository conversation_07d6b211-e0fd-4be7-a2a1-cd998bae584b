// @ts-nocheck
// File: src/routes/dashboard/automation/[id]/+page.server.ts
import { redirect } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';
import type { PageServerLoad } from '../../$types.js';

// Using the shared Prisma client from $lib/server/prisma

export const load = async ({ params, locals }: Parameters<PageServerLoad>[0]) => {
  try {
    const user = locals.user;
    const { id } = params;

    if (!user) {
      throw redirect(302, '/auth/sign-in');
    }

    // Get the automation run with related data
    const automationRun = await prisma.automationRun.findFirst({
      where: {
        id,
        OR: [
          { userId: user.id },
          {
            profile: {
              team: {
                members: {
                  some: { userId: user.id },
                },
              },
            },
          },
        ],
      },
      include: {
        profile: {
          include: {
            data: true,
          },
        },
      },
    });

    if (!automationRun) {
      throw redirect(302, '/dashboard/automation');
    }

    // If automation run found, get job listings from cron schema using matchedJobIds
    let jobListings = [];
    if (automationRun.matchedJobIds && automationRun.matchedJobIds.length > 0) {
      try {
        // Get job listings from cron schema
        jobListings = await prisma.job_listing.findMany({
          where: {
            id: {
              in: automationRun.matchedJobIds,
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        });
      } catch (error) {
        console.error('Error fetching job listings from cron schema:', error);
        // No fallback since jobs relation doesn't exist
        jobListings = [];
      }
    }

    return {
      user,
      automationRun,
      jobs: jobListings,
      profile: automationRun.profile,
    };
  } catch (error) {
    console.error('Error in automation details page load:', error);

    // If it's a redirect, re-throw it
    if (error?.status === 302) {
      throw error;
    }

    // For other errors, redirect to automation list
    throw redirect(302, '/dashboard/automation');
  }
};
